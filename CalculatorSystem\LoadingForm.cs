using System;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace CalculatorSystem
{
    public partial class LoadingForm : Form
    {
        private CancellationTokenSource cancellationTokenSource;
        private bool isCancelled = false;

        public bool IsCancelled => isCancelled;
        public CancellationToken CancellationToken => cancellationTokenSource?.Token ?? CancellationToken.None;

        public LoadingForm(string message = "正在加载数据，请稍候...")
        {
            InitializeComponent();
            lblMessage.Text = message;
            cancellationTokenSource = new CancellationTokenSource();

            // 确保窗口居中显示
            this.Load += LoadingForm_Load;
        }

        private void LoadingForm_Load(object sender, EventArgs e)
        {
            // 如果有父窗口，相对于父窗口居中
            if (this.Owner != null)
            {
                this.Location = new Point(
                    this.Owner.Location.X + (this.Owner.Width - this.Width) / 2,
                    this.Owner.Location.Y + (this.Owner.Height - this.Height) / 2
                );
            }
        }

        private void InitializeComponent()
        {
            this.lblMessage = new System.Windows.Forms.Label();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.btnCancel = new System.Windows.Forms.Button();
            this.pictureBox = new System.Windows.Forms.PictureBox();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox)).BeginInit();
            this.SuspendLayout();
            
            // 
            // LoadingForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(400, 180);
            this.ControlBox = false;
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "LoadingForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "加载中";
            this.TopMost = true;
            
            // 
            // pictureBox
            // 
            this.pictureBox.Location = new System.Drawing.Point(30, 30);
            this.pictureBox.Name = "pictureBox";
            this.pictureBox.Size = new System.Drawing.Size(32, 32);
            this.pictureBox.TabIndex = 0;
            this.pictureBox.TabStop = false;
            
            // 
            // lblMessage
            // 
            this.lblMessage.AutoSize = false;
            this.lblMessage.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblMessage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMessage.Location = new System.Drawing.Point(80, 35);
            this.lblMessage.Name = "lblMessage";
            this.lblMessage.Size = new System.Drawing.Size(300, 25);
            this.lblMessage.TabIndex = 1;
            this.lblMessage.Text = "正在加载数据，请稍候...";
            this.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            
            // 
            // progressBar
            // 
            this.progressBar.Location = new System.Drawing.Point(30, 80);
            this.progressBar.MarqueeAnimationSpeed = 30;
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(340, 23);
            this.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Marquee;
            this.progressBar.TabIndex = 2;
            
            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(160, 125);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(80, 30);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            
            this.Controls.Add(this.pictureBox);
            this.Controls.Add(this.lblMessage);
            this.Controls.Add(this.progressBar);
            this.Controls.Add(this.btnCancel);
            
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox)).EndInit();
            this.ResumeLayout(false);
        }

        private System.Windows.Forms.Label lblMessage;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.PictureBox pictureBox;

        private void btnCancel_Click(object sender, EventArgs e)
        {
            isCancelled = true;
            cancellationTokenSource?.Cancel();
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        public void UpdateMessage(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateMessage), message);
                return;
            }
            lblMessage.Text = message;
        }

        public void UpdateProgress(int percentage)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int>(UpdateProgress), percentage);
                return;
            }

            if (progressBar.Style != ProgressBarStyle.Continuous)
            {
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Minimum = 0;
                progressBar.Maximum = 100;
            }

            progressBar.Value = Math.Max(0, Math.Min(100, percentage));
        }

        public void SetMarqueeMode(bool marquee)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<bool>(SetMarqueeMode), marquee);
                return;
            }

            progressBar.Style = marquee ? ProgressBarStyle.Marquee : ProgressBarStyle.Continuous;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (!isCancelled)
            {
                isCancelled = true;
                cancellationTokenSource?.Cancel();
            }
            base.OnFormClosing(e);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                cancellationTokenSource?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
