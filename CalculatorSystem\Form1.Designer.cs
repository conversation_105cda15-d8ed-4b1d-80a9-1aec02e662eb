namespace CalculatorSystem
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        private System.Windows.Forms.GroupBox groupBoxFile;
        private System.Windows.Forms.Button btnSelectFile;
        private System.Windows.Forms.TextBox txtFilePath;
        private System.Windows.Forms.Label lblFilePath;
        private System.Windows.Forms.GroupBox groupBoxSettings;
        private System.Windows.Forms.NumericUpDown numMaxNumber;
        private System.Windows.Forms.Label lblMaxNumber;
        private System.Windows.Forms.NumericUpDown numMagneticPoints;
        private System.Windows.Forms.Label lblMagneticPoints;
        private System.Windows.Forms.NumericUpDown numResultMax;
        private System.Windows.Forms.NumericUpDown numResultMin;
        private System.Windows.Forms.Label lblResultRange;
        private System.Windows.Forms.NumericUpDown numCombinationCount;
        private System.Windows.Forms.Label lblCombinationCount;
        private System.Windows.Forms.RadioButton radioBtnRangeMode;
        private System.Windows.Forms.RadioButton radioBtnExcludeMode;
        private System.Windows.Forms.Label lblOutputMode;
        private System.Windows.Forms.GroupBox groupBoxProcess;
        private System.Windows.Forms.Button btnStart;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.OpenFileDialog openFileDialog;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxFile = new System.Windows.Forms.GroupBox();
            this.btnSelectFile = new System.Windows.Forms.Button();
            this.txtFilePath = new System.Windows.Forms.TextBox();
            this.lblFilePath = new System.Windows.Forms.Label();
            this.groupBoxSettings = new System.Windows.Forms.GroupBox();
            this.numMaxNumber = new System.Windows.Forms.NumericUpDown();
            this.lblMaxNumber = new System.Windows.Forms.Label();
            this.numMagneticPoints = new System.Windows.Forms.NumericUpDown();
            this.lblMagneticPoints = new System.Windows.Forms.Label();
            this.numResultMax = new System.Windows.Forms.NumericUpDown();
            this.numResultMin = new System.Windows.Forms.NumericUpDown();
            this.lblResultRange = new System.Windows.Forms.Label();
            this.numCombinationCount = new System.Windows.Forms.NumericUpDown();
            this.lblCombinationCount = new System.Windows.Forms.Label();
            this.radioBtnRangeMode = new System.Windows.Forms.RadioButton();
            this.radioBtnExcludeMode = new System.Windows.Forms.RadioButton();
            this.lblOutputMode = new System.Windows.Forms.Label();
            this.groupBoxProcess = new System.Windows.Forms.GroupBox();
            this.btnStart = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.lblStatus = new System.Windows.Forms.Label();
            this.openFileDialog = new System.Windows.Forms.OpenFileDialog();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.groupBoxTwoStage = new System.Windows.Forms.GroupBox();
            this.btnClearSelection = new System.Windows.Forms.Button();
            this.btnSelectAll = new System.Windows.Forms.Button();
            this.lblSelectedCount = new System.Windows.Forms.Label();
            this.btnExportSelected = new System.Windows.Forms.Button();
            this.lblCombinationList = new System.Windows.Forms.Label();
            this.lblFrequencyList = new System.Windows.Forms.Label();
            this.listViewCombinations = new System.Windows.Forms.ListView();
            this.listViewFrequency = new System.Windows.Forms.ListView();
            this.groupBoxFile.SuspendLayout();
            this.groupBoxSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMagneticPoints)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCombinationCount)).BeginInit();
            this.groupBoxProcess.SuspendLayout();
            this.groupBoxTwoStage.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxFile
            // 
            this.groupBoxFile.Controls.Add(this.btnSelectFile);
            this.groupBoxFile.Controls.Add(this.txtFilePath);
            this.groupBoxFile.Controls.Add(this.lblFilePath);
            this.groupBoxFile.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxFile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxFile.Location = new System.Drawing.Point(20, 20);
            this.groupBoxFile.Name = "groupBoxFile";
            this.groupBoxFile.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxFile.Size = new System.Drawing.Size(800, 90);
            this.groupBoxFile.TabIndex = 0;
            this.groupBoxFile.TabStop = false;
            this.groupBoxFile.Text = "📁 数据文件";
            // 
            // btnSelectFile
            // 
            this.btnSelectFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnSelectFile.FlatAppearance.BorderSize = 0;
            this.btnSelectFile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(102)))), ((int)(((byte)(184)))));
            this.btnSelectFile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(28)))), ((int)(((byte)(151)))), ((int)(((byte)(234)))));
            this.btnSelectFile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSelectFile.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSelectFile.ForeColor = System.Drawing.Color.White;
            this.btnSelectFile.Location = new System.Drawing.Point(680, 35);
            this.btnSelectFile.Name = "btnSelectFile";
            this.btnSelectFile.Size = new System.Drawing.Size(100, 35);
            this.btnSelectFile.TabIndex = 2;
            this.btnSelectFile.Text = "选择文件";
            this.btnSelectFile.UseVisualStyleBackColor = false;
            this.btnSelectFile.Click += new System.EventHandler(this.btnSelectFile_Click);
            // 
            // txtFilePath
            // 
            this.txtFilePath.BackColor = System.Drawing.Color.White;
            this.txtFilePath.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtFilePath.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtFilePath.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.txtFilePath.Location = new System.Drawing.Point(90, 40);
            this.txtFilePath.Name = "txtFilePath";
            this.txtFilePath.ReadOnly = true;
            this.txtFilePath.Size = new System.Drawing.Size(580, 23);
            this.txtFilePath.TabIndex = 1;
            // 
            // lblFilePath
            // 
            this.lblFilePath.AutoSize = true;
            this.lblFilePath.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblFilePath.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblFilePath.Location = new System.Drawing.Point(20, 42);
            this.lblFilePath.Name = "lblFilePath";
            this.lblFilePath.Size = new System.Drawing.Size(59, 17);
            this.lblFilePath.TabIndex = 0;
            this.lblFilePath.Text = "文件路径:";
            // 
            // groupBoxSettings
            // 
            this.groupBoxSettings.Controls.Add(this.numMaxNumber);
            this.groupBoxSettings.Controls.Add(this.lblMaxNumber);
            this.groupBoxSettings.Controls.Add(this.numMagneticPoints);
            this.groupBoxSettings.Controls.Add(this.lblMagneticPoints);
            this.groupBoxSettings.Controls.Add(this.numResultMax);
            this.groupBoxSettings.Controls.Add(this.numResultMin);
            this.groupBoxSettings.Controls.Add(this.lblResultRange);
            this.groupBoxSettings.Controls.Add(this.numCombinationCount);
            this.groupBoxSettings.Controls.Add(this.lblCombinationCount);
            this.groupBoxSettings.Controls.Add(this.radioBtnRangeMode);
            this.groupBoxSettings.Controls.Add(this.radioBtnExcludeMode);
            this.groupBoxSettings.Controls.Add(this.lblOutputMode);
            this.groupBoxSettings.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxSettings.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxSettings.Location = new System.Drawing.Point(20, 143);
            this.groupBoxSettings.Name = "groupBoxSettings";
            this.groupBoxSettings.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxSettings.Size = new System.Drawing.Size(800, 150);
            this.groupBoxSettings.TabIndex = 1;
            this.groupBoxSettings.TabStop = false;
            this.groupBoxSettings.Text = "⚙️ 参数设置";
            // 
            // numMaxNumber
            // 
            this.numMaxNumber.BackColor = System.Drawing.Color.White;
            this.numMaxNumber.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numMaxNumber.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMaxNumber.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numMaxNumber.Location = new System.Drawing.Point(142, 45);
            this.numMaxNumber.Maximum = new decimal(new int[] {
            37,
            0,
            0,
            0});
            this.numMaxNumber.Minimum = new decimal(new int[] {
            33,
            0,
            0,
            0});
            this.numMaxNumber.Name = "numMaxNumber";
            this.numMaxNumber.Size = new System.Drawing.Size(90, 23);
            this.numMaxNumber.TabIndex = 1;
            this.numMaxNumber.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numMaxNumber.Value = new decimal(new int[] {
            33,
            0,
            0,
            0});
            // 
            // lblMaxNumber
            // 
            this.lblMaxNumber.AutoSize = true;
            this.lblMaxNumber.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblMaxNumber.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMaxNumber.Location = new System.Drawing.Point(22, 47);
            this.lblMaxNumber.Name = "lblMaxNumber";
            this.lblMaxNumber.Size = new System.Drawing.Size(100, 17);
            this.lblMaxNumber.TabIndex = 0;
            this.lblMaxNumber.Text = "最大数值(33-37):";
            // 
            // numMagneticPoints
            // 
            this.numMagneticPoints.BackColor = System.Drawing.Color.White;
            this.numMagneticPoints.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numMagneticPoints.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMagneticPoints.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numMagneticPoints.Location = new System.Drawing.Point(392, 45);
            this.numMagneticPoints.Maximum = new decimal(new int[] {
            9,
            0,
            0,
            0});
            this.numMagneticPoints.Minimum = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numMagneticPoints.Name = "numMagneticPoints";
            this.numMagneticPoints.Size = new System.Drawing.Size(90, 23);
            this.numMagneticPoints.TabIndex = 3;
            this.numMagneticPoints.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numMagneticPoints.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // lblMagneticPoints
            // 
            this.lblMagneticPoints.AutoSize = true;
            this.lblMagneticPoints.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblMagneticPoints.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMagneticPoints.Location = new System.Drawing.Point(272, 47);
            this.lblMagneticPoints.Name = "lblMagneticPoints";
            this.lblMagneticPoints.Size = new System.Drawing.Size(98, 17);
            this.lblMagneticPoints.TabIndex = 2;
            this.lblMagneticPoints.Text = "磁控点位数(5-9):";
            // 
            // numResultMax
            // 
            this.numResultMax.BackColor = System.Drawing.Color.White;
            this.numResultMax.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numResultMax.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numResultMax.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numResultMax.Location = new System.Drawing.Point(710, 45);
            this.numResultMax.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numResultMax.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numResultMax.Name = "numResultMax";
            this.numResultMax.Size = new System.Drawing.Size(70, 23);
            this.numResultMax.TabIndex = 9;
            this.numResultMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numResultMax.Value = new decimal(new int[] {
            15,
            0,
            0,
            0});
            // 
            // numResultMin
            // 
            this.numResultMin.BackColor = System.Drawing.Color.White;
            this.numResultMin.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numResultMin.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numResultMin.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numResultMin.Location = new System.Drawing.Point(623, 45);
            this.numResultMin.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numResultMin.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numResultMin.Name = "numResultMin";
            this.numResultMin.Size = new System.Drawing.Size(70, 23);
            this.numResultMin.TabIndex = 8;
            this.numResultMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numResultMin.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // lblResultRange
            // 
            this.lblResultRange.AutoSize = true;
            this.lblResultRange.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblResultRange.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblResultRange.Location = new System.Drawing.Point(510, 47);
            this.lblResultRange.Name = "lblResultRange";
            this.lblResultRange.Size = new System.Drawing.Size(107, 17);
            this.lblResultRange.TabIndex = 7;
            this.lblResultRange.Text = "最终结果频次范围:";
            // 
            // numCombinationCount
            // 
            this.numCombinationCount.BackColor = System.Drawing.Color.White;
            this.numCombinationCount.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.numCombinationCount.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numCombinationCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.numCombinationCount.Location = new System.Drawing.Point(142, 85);
            this.numCombinationCount.Maximum = new decimal(new int[] {
            8,
            0,
            0,
            0});
            this.numCombinationCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numCombinationCount.Name = "numCombinationCount";
            this.numCombinationCount.Size = new System.Drawing.Size(90, 23);
            this.numCombinationCount.TabIndex = 10;
            this.numCombinationCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numCombinationCount.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // lblCombinationCount
            // 
            this.lblCombinationCount.AutoSize = true;
            this.lblCombinationCount.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblCombinationCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCombinationCount.Location = new System.Drawing.Point(22, 87);
            this.lblCombinationCount.Name = "lblCombinationCount";
            this.lblCombinationCount.Size = new System.Drawing.Size(86, 17);
            this.lblCombinationCount.TabIndex = 11;
            this.lblCombinationCount.Text = "组合位数(1-8):";
            // 
            // radioBtnRangeMode
            // 
            this.radioBtnRangeMode.AutoSize = true;
            this.radioBtnRangeMode.Checked = true;
            this.radioBtnRangeMode.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.radioBtnRangeMode.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.radioBtnRangeMode.Location = new System.Drawing.Point(392, 85);
            this.radioBtnRangeMode.Name = "radioBtnRangeMode";
            this.radioBtnRangeMode.Size = new System.Drawing.Size(74, 21);
            this.radioBtnRangeMode.TabIndex = 12;
            this.radioBtnRangeMode.TabStop = true;
            this.radioBtnRangeMode.Text = "范围模式";
            this.radioBtnRangeMode.UseVisualStyleBackColor = true;
            // 
            // radioBtnExcludeMode
            // 
            this.radioBtnExcludeMode.AutoSize = true;
            this.radioBtnExcludeMode.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.radioBtnExcludeMode.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.radioBtnExcludeMode.Location = new System.Drawing.Point(480, 85);
            this.radioBtnExcludeMode.Name = "radioBtnExcludeMode";
            this.radioBtnExcludeMode.Size = new System.Drawing.Size(74, 21);
            this.radioBtnExcludeMode.TabIndex = 13;
            this.radioBtnExcludeMode.Text = "剔除模式";
            this.radioBtnExcludeMode.UseVisualStyleBackColor = true;
            // 
            // lblOutputMode
            // 
            this.lblOutputMode.AutoSize = true;
            this.lblOutputMode.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblOutputMode.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblOutputMode.Location = new System.Drawing.Point(272, 87);
            this.lblOutputMode.Name = "lblOutputMode";
            this.lblOutputMode.Size = new System.Drawing.Size(59, 17);
            this.lblOutputMode.TabIndex = 14;
            this.lblOutputMode.Text = "输出模式:";
            // 
            // groupBoxProcess
            // 
            this.groupBoxProcess.Controls.Add(this.btnStart);
            this.groupBoxProcess.Controls.Add(this.btnCancel);
            this.groupBoxProcess.Controls.Add(this.progressBar);
            this.groupBoxProcess.Controls.Add(this.lblStatus);
            this.groupBoxProcess.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxProcess.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxProcess.Location = new System.Drawing.Point(20, 320);
            this.groupBoxProcess.Name = "groupBoxProcess";
            this.groupBoxProcess.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxProcess.Size = new System.Drawing.Size(800, 120);
            this.groupBoxProcess.TabIndex = 2;
            this.groupBoxProcess.TabStop = false;
            this.groupBoxProcess.Text = "⚡ 处理进度";
            // 
            // btnStart
            // 
            this.btnStart.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnStart.FlatAppearance.BorderSize = 0;
            this.btnStart.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(174)))), ((int)(((byte)(96)))));
            this.btnStart.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(231)))), ((int)(((byte)(128)))));
            this.btnStart.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStart.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStart.ForeColor = System.Drawing.Color.White;
            this.btnStart.Location = new System.Drawing.Point(20, 70);
            this.btnStart.Name = "btnStart";
            this.btnStart.Size = new System.Drawing.Size(120, 40);
            this.btnStart.TabIndex = 0;
            this.btnStart.Text = "开始计算";
            this.btnStart.UseVisualStyleBackColor = false;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnCancel.Enabled = false;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(57)))), ((int)(((byte)(43)))));
            this.btnCancel.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(241)))), ((int)(((byte)(148)))), ((int)(((byte)(138)))));
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(160, 70);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(120, 40);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消计算";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // progressBar
            // 
            this.progressBar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(241)))));
            this.progressBar.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.progressBar.Location = new System.Drawing.Point(20, 35);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(760, 25);
            this.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Continuous;
            this.progressBar.TabIndex = 2;
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblStatus.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(127)))), ((int)(((byte)(140)))), ((int)(((byte)(141)))));
            this.lblStatus.Location = new System.Drawing.Point(286, 82);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(56, 17);
            this.lblStatus.TabIndex = 3;
            this.lblStatus.Text = "准备就绪";
            // 
            // openFileDialog
            // 
            this.openFileDialog.Filter = "文本文件|*.txt|所有文件|*.*";
            this.openFileDialog.Title = "选择数据文件";
            // 
            // saveFileDialog
            // 
            this.saveFileDialog.Filter = "文本文件|*.txt|所有文件|*.*";
            this.saveFileDialog.Title = "保存结果文件";
            // 
            // groupBoxTwoStage
            // 
            this.groupBoxTwoStage.Controls.Add(this.listViewFrequency);
            this.groupBoxTwoStage.Controls.Add(this.listViewCombinations);
            this.groupBoxTwoStage.Controls.Add(this.lblFrequencyList);
            this.groupBoxTwoStage.Controls.Add(this.lblCombinationList);
            this.groupBoxTwoStage.Controls.Add(this.btnExportSelected);
            this.groupBoxTwoStage.Controls.Add(this.lblSelectedCount);
            this.groupBoxTwoStage.Controls.Add(this.btnSelectAll);
            this.groupBoxTwoStage.Controls.Add(this.btnClearSelection);
            this.groupBoxTwoStage.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxTwoStage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.groupBoxTwoStage.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxTwoStage.Location = new System.Drawing.Point(20, 470);
            this.groupBoxTwoStage.Name = "groupBoxTwoStage";
            this.groupBoxTwoStage.Padding = new System.Windows.Forms.Padding(15, 10, 15, 15);
            this.groupBoxTwoStage.Size = new System.Drawing.Size(800, 380);
            this.groupBoxTwoStage.TabIndex = 4;
            this.groupBoxTwoStage.TabStop = false;
            this.groupBoxTwoStage.Text = "📊 组合分析";
            this.groupBoxTwoStage.Visible = true;
            // 
            // btnClearSelection
            // 
            this.btnClearSelection.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(149)))), ((int)(((byte)(165)))), ((int)(((byte)(166)))));
            this.btnClearSelection.FlatAppearance.BorderSize = 0;
            this.btnClearSelection.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(127)))), ((int)(((byte)(140)))), ((int)(((byte)(141)))));
            this.btnClearSelection.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(174)))), ((int)(((byte)(182)))), ((int)(((byte)(191)))));
            this.btnClearSelection.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnClearSelection.Font = new System.Drawing.Font("微软雅黑", 8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnClearSelection.ForeColor = System.Drawing.Color.White;
            this.btnClearSelection.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnClearSelection.Location = new System.Drawing.Point(330, 340);
            this.btnClearSelection.Name = "btnClearSelection";
            this.btnClearSelection.Size = new System.Drawing.Size(80, 30);
            this.btnClearSelection.TabIndex = 7;
            this.btnClearSelection.Text = "清空选择";
            this.btnClearSelection.UseVisualStyleBackColor = false;
            this.btnClearSelection.Click += new System.EventHandler(this.btnClearSelection_Click);
            // 
            // btnSelectAll
            // 
            this.btnSelectAll.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnSelectAll.FlatAppearance.BorderSize = 0;
            this.btnSelectAll.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(41)))), ((int)(((byte)(128)))), ((int)(((byte)(185)))));
            this.btnSelectAll.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(93)))), ((int)(((byte)(173)))), ((int)(((byte)(226)))));
            this.btnSelectAll.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSelectAll.Font = new System.Drawing.Font("微软雅黑", 8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSelectAll.ForeColor = System.Drawing.Color.White;
            this.btnSelectAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnSelectAll.Location = new System.Drawing.Point(240, 340);
            this.btnSelectAll.Name = "btnSelectAll";
            this.btnSelectAll.Size = new System.Drawing.Size(80, 30);
            this.btnSelectAll.TabIndex = 6;
            this.btnSelectAll.Text = "全选";
            this.btnSelectAll.UseVisualStyleBackColor = false;
            this.btnSelectAll.Click += new System.EventHandler(this.btnSelectAll_Click);
            // 
            // lblSelectedCount
            // 
            this.lblSelectedCount.AutoSize = true;
            this.lblSelectedCount.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblSelectedCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblSelectedCount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lblSelectedCount.Location = new System.Drawing.Point(20, 345);
            this.lblSelectedCount.Name = "lblSelectedCount";
            this.lblSelectedCount.Size = new System.Drawing.Size(98, 17);
            this.lblSelectedCount.TabIndex = 4;
            this.lblSelectedCount.Text = "已选择: 0 个组合";
            // 
            // btnExportSelected
            // 
            this.btnExportSelected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.btnExportSelected.Enabled = false;
            this.btnExportSelected.FlatAppearance.BorderSize = 0;
            this.btnExportSelected.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(68)))), ((int)(((byte)(173)))));
            this.btnExportSelected.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(187)))), ((int)(((byte)(143)))), ((int)(((byte)(206)))));
            this.btnExportSelected.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnExportSelected.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExportSelected.ForeColor = System.Drawing.Color.White;
            this.btnExportSelected.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExportSelected.Location = new System.Drawing.Point(680, 340);
            this.btnExportSelected.Name = "btnExportSelected";
            this.btnExportSelected.Size = new System.Drawing.Size(100, 30);
            this.btnExportSelected.TabIndex = 5;
            this.btnExportSelected.Text = "导出选中";
            this.btnExportSelected.UseVisualStyleBackColor = false;
            this.btnExportSelected.Click += new System.EventHandler(this.btnExportSelected_Click);
            // 
            // lblCombinationList
            // 
            this.lblCombinationList.AutoSize = true;
            this.lblCombinationList.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblCombinationList.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblCombinationList.Location = new System.Drawing.Point(240, 30);
            this.lblCombinationList.Name = "lblCombinationList";
            this.lblCombinationList.Size = new System.Drawing.Size(68, 17);
            this.lblCombinationList.TabIndex = 2;
            this.lblCombinationList.Text = "具体组合：";
            // 
            // lblFrequencyList
            // 
            this.lblFrequencyList.AutoSize = true;
            this.lblFrequencyList.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblFrequencyList.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblFrequencyList.Location = new System.Drawing.Point(20, 30);
            this.lblFrequencyList.Name = "lblFrequencyList";
            this.lblFrequencyList.Size = new System.Drawing.Size(68, 17);
            this.lblFrequencyList.TabIndex = 0;
            this.lblFrequencyList.Text = "频次统计：";
            // 
            // listViewCombinations
            // 
            this.listViewCombinations.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.listViewCombinations.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listViewCombinations.Font = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listViewCombinations.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.listViewCombinations.FullRowSelect = true;
            this.listViewCombinations.GridLines = true;
            this.listViewCombinations.HideSelection = false;
            this.listViewCombinations.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewCombinations.Location = new System.Drawing.Point(240, 50);
            this.listViewCombinations.Name = "listViewCombinations";
            this.listViewCombinations.Size = new System.Drawing.Size(540, 280);
            this.listViewCombinations.TabIndex = 3;
            this.listViewCombinations.UseCompatibleStateImageBehavior = false;
            this.listViewCombinations.View = System.Windows.Forms.View.Details;
            // 
            // listViewFrequency
            // 
            this.listViewFrequency.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.listViewFrequency.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.listViewFrequency.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.listViewFrequency.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.listViewFrequency.FullRowSelect = true;
            this.listViewFrequency.GridLines = true;
            this.listViewFrequency.HideSelection = false;
            this.listViewFrequency.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)));
            this.listViewFrequency.Location = new System.Drawing.Point(20, 50);
            this.listViewFrequency.Name = "listViewFrequency";
            this.listViewFrequency.Size = new System.Drawing.Size(200, 280);
            this.listViewFrequency.TabIndex = 1;
            this.listViewFrequency.UseCompatibleStateImageBehavior = false;
            this.listViewFrequency.View = System.Windows.Forms.View.Details;
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.ClientSize = new System.Drawing.Size(840, 879);
            this.Controls.Add(this.groupBoxTwoStage);
            this.Controls.Add(this.groupBoxProcess);
            this.Controls.Add(this.groupBoxSettings);
            this.Controls.Add(this.groupBoxFile);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimumSize = new System.Drawing.Size(860, 700);
            this.Name = "Form1";
            this.Size = new System.Drawing.Size(860, 900);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "离子镀膜磁控点分布运算系统";
            this.groupBoxFile.ResumeLayout(false);
            this.groupBoxFile.PerformLayout();
            this.groupBoxSettings.ResumeLayout(false);
            this.groupBoxSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMagneticPoints)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numResultMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCombinationCount)).EndInit();
            this.groupBoxProcess.ResumeLayout(false);
            this.groupBoxProcess.PerformLayout();
            this.groupBoxTwoStage.ResumeLayout(false);
            this.groupBoxTwoStage.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxTwoStage;
        private System.Windows.Forms.ListView listViewFrequency;
        private System.Windows.Forms.ListView listViewCombinations;
        private System.Windows.Forms.Label lblFrequencyList;
        private System.Windows.Forms.Label lblCombinationList;
        private System.Windows.Forms.Button btnExportSelected;
        private System.Windows.Forms.Label lblSelectedCount;
        private System.Windows.Forms.Button btnSelectAll;
        private System.Windows.Forms.Button btnClearSelection;
    }
}

