using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Threading;

namespace CalculatorSystem
{
    public partial class Form1 : Form
    {
        private CancellationTokenSource cancellationTokenSource;
        private List<string> originalData;
        private bool isProcessing = false;

        // N数组合分析相关字段
        private Dictionary<int, List<string>> frequencyCombinations; // 频次 -> 组合列表
        private Dictionary<string, int> nCombinationCounts; // 组合 -> 频次
        private HashSet<string> selectedCombinations; // 保存跨频次选择的组合
        private HashSet<string> excludedCombinations; // 剔除模式下被排除的组合

        public Form1()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            originalData = new List<string>();
            frequencyCombinations = new Dictionary<int, List<string>>();
            nCombinationCounts = new Dictionary<string, int>();
            selectedCombinations = new HashSet<string>();
            excludedCombinations = new HashSet<string>();

            // 设置默认值
            numMaxNumber.Value = 33;
            numMagneticPoints.Value = 5;
            numCombinationCount.Value = 2;
            numResultMin.Value = 10;
            numResultMax.Value = 15;
            radioBtnRangeMode.Checked = true;

            // 添加事件处理器
            numCombinationCount.ValueChanged += NumCombinationCount_ValueChanged;
            radioBtnExcludeMode.CheckedChanged += RadioBtnExcludeMode_CheckedChanged;

            // 设置进度条
            progressBar.Minimum = 0;
            progressBar.Maximum = 100;
            progressBar.Value = 0;

            // 初始化ListView
            InitializeListViews();

            UpdateUI();
        }

        /// <summary>
        /// 初始化ListView控件
        /// </summary>
        private void InitializeListViews()
        {
            // 初始化频次列表
            listViewFrequency.Columns.Add("频次", 80);
            listViewFrequency.Columns.Add("组合数", 100);
            listViewFrequency.SelectedIndexChanged += ListViewFrequency_SelectedIndexChanged;

            // 初始化组合列表
            listViewCombinations.Columns.Add("序号", 80);
            listViewCombinations.Columns.Add("组合", 300);
            listViewCombinations.Columns.Add("频次", 100);
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;
            listViewCombinations.CheckBoxes = true;
            listViewCombinations.Resize += ListViewCombinations_Resize;
        }

        /// <summary>
        /// 更新界面状态
        /// </summary>
        private void UpdateUI()
        {
            btnStart.Enabled = !isProcessing && !string.IsNullOrEmpty(txtFilePath.Text);
            btnCancel.Enabled = isProcessing;

            groupBoxSettings.Enabled = !isProcessing;
            groupBoxFile.Enabled = !isProcessing;

            // 始终显示N数组合分析界面
         
            groupBoxTwoStage.Visible = true;
            btnStart.Text = $"分析{numCombinationCount.Value}数组合";

            // 更新导出按钮状态
            UpdateExportButtonState();

            // 按钮状态控制
            btnSelectAll.Enabled = !isProcessing && listViewCombinations.Items.Count > 0;
            btnClearSelection.Enabled = !isProcessing && selectedCombinations.Count > 0;
        }

        /// <summary>
        /// 更新导出按钮状态
        /// </summary>
        private void UpdateExportButtonState()
        {
            int selectedCount = GetSelectedCombinationsCount();
            btnExportSelected.Enabled = selectedCount > 0 && !isProcessing;

            if (selectedCount > 0)
            {
                // 统计选中组合的频次分布
                var frequencyStats = selectedCombinations
                    .Where(c => nCombinationCounts.ContainsKey(c))
                    .GroupBy(c => nCombinationCounts[c])
                    .OrderBy(g => g.Key)
                    .Select(g => $"频次{g.Key}({g.Count()}个)")
                    .ToList();

                string statsText = string.Join(", ", frequencyStats);
                lblSelectedCount.Text = $"已选择: {selectedCount} 个组合";
            }
            else
            {
                lblSelectedCount.Text = "已选择: 0 个组合";
            }
        }

        /// <summary>
        /// 获取选中的组合数量
        /// </summary>
        private int GetSelectedCombinationsCount()
        {
            return selectedCombinations.Count;
        }

        /// <summary>
        /// 频次列表选择变化事件
        /// </summary>
        private void ListViewFrequency_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewFrequency.SelectedItems.Count > 0)
            {
                ShowCombinationsForSelectedFrequencies();
            }
        }

        /// <summary>
        /// 组合列表选择变化事件
        /// </summary>
        private void ListViewCombinations_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            // 更新全局选择状态
            var itemData = (dynamic)e.Item.Tag;
            string combination = itemData.Combination;

            if (e.Item.Checked)
            {
                selectedCombinations.Add(combination);
            }
            else
            {
                selectedCombinations.Remove(combination);
            }

            UpdateExportButtonState();
        }

        /// <summary>
        /// 选择文件按钮点击事件
        /// </summary>
        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = openFileDialog.FileName;
                LoadDataFile();
                UpdateUI();
            }
        }

        /// <summary>
        /// 加载数据文件
        /// </summary>
        private void LoadDataFile()
        {
            try
            {
                originalData.Clear();
                string[] lines = File.ReadAllLines(txtFilePath.Text, Encoding.UTF8);

                int validLines = 0;
                int totalDataLines = 0; // 统计实际数据行数（考虑频次）

                for (int i = 0; i < lines.Length; i++)
                {
                    string line = lines[i].Trim();
                    if (!string.IsNullOrEmpty(line))
                    {
                        var parseResult = ParseDataLineWithFrequency(line, i + 1);
                        if (parseResult.IsValid)
                        {
                            // 根据频次添加对应数量的数据行
                            for (int j = 0; j < parseResult.Frequency; j++)
                            {
                                originalData.Add(parseResult.DataLine);
                                totalDataLines++;
                            }
                            validLines++;
                        }
                    }
                }

                lblStatus.Text = $"已加载 {validLines} 行有效数据（共 {totalDataLines} 条记录，含频次计算）";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "文件加载失败";
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 数据行解析结果
        /// </summary>
        private class DataLineParseResult
        {
            public bool IsValid { get; set; }
            public string DataLine { get; set; }
            public int Frequency { get; set; }
        }

        /// <summary>
        /// 解析带频次的数据行
        /// </summary>
        private DataLineParseResult ParseDataLineWithFrequency(string line, int lineNumber)
        {
            var result = new DataLineParseResult { IsValid = false, Frequency = 1 };

            try
            {
                string dataLine = line;
                int frequency = 1;

                // 检查是否有频次后缀（空格+数字）
                int lastSpaceIndex = line.LastIndexOf(' ');
                if (lastSpaceIndex > 0 && lastSpaceIndex < line.Length - 1)
                {
                    string possibleFrequency = line.Substring(lastSpaceIndex + 1);
                    if (int.TryParse(possibleFrequency, out int parsedFrequency) && parsedFrequency > 0)
                    {
                        frequency = parsedFrequency;
                        dataLine = line.Substring(0, lastSpaceIndex).Trim();
                    }
                }

                // 验证数据行格式
                if (ValidateDataLine(dataLine, lineNumber))
                {
                    result.IsValid = true;
                    result.DataLine = dataLine;
                    result.Frequency = frequency;
                }

                return result;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"第 {lineNumber} 行数据解析失败: {ex.Message}", "数据解析错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return result;
            }
        }

        /// <summary>
        /// 验证数据行格式
        /// </summary>
        private bool ValidateDataLine(string line, int lineNumber)
        {
            try
            {
                string[] parts = line.Split('-');
                if (parts.Length < 2)
                {
                    MessageBox.Show($"第 {lineNumber} 行数据格式错误: 至少需要2个数字", "数据验证错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                List<int> numbers = new List<int>();
                foreach (string part in parts)
                {
                    if (!int.TryParse(part.Trim(), out int number))
                    {
                        MessageBox.Show($"第 {lineNumber} 行包含无效数字: {part}", "数据验证错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }

                    if (number < 1 || number > (int)numMaxNumber.Value)
                    {
                        MessageBox.Show($"第 {lineNumber} 行数字超出范围(1-{numMaxNumber.Value}): {number}",
                            "数据验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }

                    numbers.Add(number);
                }

                // 检查是否按升序排列且无重复
                for (int i = 1; i < numbers.Count; i++)
                {
                    if (numbers[i] <= numbers[i - 1])
                    {
                        MessageBox.Show($"第 {lineNumber} 行数据未按升序排列或存在重复: {line}",
                            "数据验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"第 {lineNumber} 行数据验证失败: {ex.Message}", "数据验证错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
        }

        /// <summary>
        /// 开始计算按钮点击事件
        /// </summary>
        private async void btnStart_Click(object sender, EventArgs e)
        {
            if (originalData.Count == 0)
            {
                MessageBox.Show("请先选择并加载数据文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            /*
            // 验证参数
            if (numTwoCombinationMin.Value > numTwoCombinationMax.Value)
            {
                MessageBox.Show("2数组合频次范围设置错误", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            */
            if (numResultMin.Value > numResultMax.Value)
            {
                MessageBox.Show("最终结果频次范围设置错误", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 直接使用两阶段分析模式
            isProcessing = true;
            cancellationTokenSource = new CancellationTokenSource();
            UpdateUI();

            try
            {
                await ProcessNStageAnalysisAsync(cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                lblStatus.Text = "计算已取消";
                lblStatus.ForeColor = Color.Orange;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "计算失败";
                lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                isProcessing = false;
                progressBar.Value = 0;
                UpdateUI();
            }
        }

        /// <summary>
        /// 取消计算按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
            }
        }

        /// <summary>
        /// N数组合分析处理
        /// </summary>
        private async Task ProcessNStageAnalysisAsync(CancellationToken cancellationToken)
        {
            // 清空之前的选择状态
            selectedCombinations.Clear();

            await Task.Run(() =>
            {
                int combinationCount = (int)numCombinationCount.Value;

                // 步骤1: 生成所有N数组合并统计频次
                UpdateStatus($"正在生成{combinationCount}数组合...", 20);
                nCombinationCounts = GenerateNCombinations(cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 按频次分组
                UpdateStatus("正在分析频次分布...", 60);
                GroupCombinationsByFrequency();

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 更新界面显示
                UpdateStatus("正在更新显示...", 90);
                UpdateFrequencyDisplay();

                UpdateStatus($"分析完成，共发现 {nCombinationCounts.Count} 个不同的{combinationCount}数组合", 100);

            }, cancellationToken);
        }

        /// <summary>
        /// 按频次分组组合
        /// </summary>
        private void GroupCombinationsByFrequency()
        {
            frequencyCombinations.Clear();

            foreach (var kvp in nCombinationCounts)
            {
                int frequency = kvp.Value;
                string combination = kvp.Key;

                if (!frequencyCombinations.ContainsKey(frequency))
                {
                    frequencyCombinations[frequency] = new List<string>();
                }

                frequencyCombinations[frequency].Add(combination);
            }
        }

        /// <summary>
        /// 更新频次显示
        /// </summary>
        private void UpdateFrequencyDisplay()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(UpdateFrequencyDisplay));
                return;
            }

            listViewFrequency.Items.Clear();

            // 按频次排序显示
            var sortedFrequencies = frequencyCombinations.Keys.OrderBy(f => f).ToList();

            foreach (int frequency in sortedFrequencies)
            {
                int count = frequencyCombinations[frequency].Count;
                var item = new ListViewItem(frequency.ToString());
                item.SubItems.Add(count.ToString());
                item.Tag = frequency;
                listViewFrequency.Items.Add(item);
            }

            lblStatus.Text = $"频次分析完成，共 {sortedFrequencies.Count} 个不同频次";
            lblStatus.ForeColor = Color.Green;
        }

        /// <summary>
        /// 显示选中频次的组合
        /// </summary>
        private void ShowCombinationsForSelectedFrequencies()
        {
            var selectedFrequencies = new List<int>();
            foreach (ListViewItem item in listViewFrequency.SelectedItems)
            {
                selectedFrequencies.Add((int)item.Tag);
            }

            if (selectedFrequencies.Count == 0)
            {
                listViewCombinations.Items.Clear();
                lblCombinationList.Text = "具体组合：";
                return;
            }

            // 计算总的组合数量
            int totalCombinations = 0;
            foreach (int frequency in selectedFrequencies)
            {
                if (frequencyCombinations.ContainsKey(frequency))
                {
                    totalCombinations += frequencyCombinations[frequency].Count;
                }
            }

            // 如果组合数量超过2000，使用异步加载
            if (totalCombinations > 2000)
            {
                LoadCombinationsAsync(selectedFrequencies, totalCombinations);
            }
            else
            {
                // 小数据量直接同步加载
                LoadCombinationsSync(selectedFrequencies);
            }
        }

        /// <summary>
        /// 异步加载组合数据（大数据量）
        /// </summary>
        private async void LoadCombinationsAsync(List<int> selectedFrequencies, int totalCombinations)
        {
            LoadingForm loadingForm = null;

            try
            {
                // 在UI线程中创建并显示加载窗口
                loadingForm = new LoadingForm($"正在加载 {totalCombinations:N0} 个组合数据，请稍候...");
                loadingForm.Owner = this;
                loadingForm.Show();

                // 强制刷新UI，确保加载窗口立即显示
                Application.DoEvents();

                // 在后台线程中处理数据
                var itemsToAdd = await Task.Run(() =>
                {
                    var items = new List<ListViewItem>();
                    int index = 1;

                    foreach (int frequency in selectedFrequencies.OrderBy(f => f))
                    {
                        // 检查取消状态
                        if (loadingForm.IsCancelled)
                            return null;

                        if (frequencyCombinations.ContainsKey(frequency))
                        {
                            var combinations = frequencyCombinations[frequency].OrderBy(c => c).ToList();

                            foreach (string combination in combinations)
                            {
                                // 频繁检查取消状态，确保响应性
                                if (loadingForm.IsCancelled)
                                    return null;

                                var item = new ListViewItem(index.ToString());
                                item.SubItems.Add(combination);
                                item.SubItems.Add(frequency.ToString());
                                item.Tag = new { Combination = combination, Frequency = frequency };

                                // 恢复之前的选择状态
                                item.Checked = selectedCombinations.Contains(combination);

                                items.Add(item);
                                index++;

                                // 每处理100个项目检查一次取消状态
                                if (index % 100 == 0 && loadingForm.IsCancelled)
                                    return null;
                            }
                        }
                    }

                    return items;
                }, loadingForm.CancellationToken);

                // 如果没有被取消，更新UI
                if (itemsToAdd != null && !loadingForm.IsCancelled)
                {
                    UpdateCombinationsList(itemsToAdd);
                }
            }
            catch (OperationCanceledException)
            {
                // 用户取消操作，不需要显示错误
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载组合数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 确保加载窗口被关闭
                if (loadingForm != null && !loadingForm.IsDisposed)
                {
                    loadingForm.Close();
                    loadingForm.Dispose();
                }
            }
        }

        /// <summary>
        /// 同步加载组合数据（小数据量）
        /// </summary>
        private void LoadCombinationsSync(List<int> selectedFrequencies)
        {
            var itemsToAdd = new List<ListViewItem>();
            int index = 1;

            foreach (int frequency in selectedFrequencies.OrderBy(f => f))
            {
                if (frequencyCombinations.ContainsKey(frequency))
                {
                    var combinations = frequencyCombinations[frequency].OrderBy(c => c).ToList();

                    foreach (string combination in combinations)
                    {
                        var item = new ListViewItem(index.ToString());
                        item.SubItems.Add(combination);
                        item.SubItems.Add(frequency.ToString());
                        item.Tag = new { Combination = combination, Frequency = frequency };

                        // 恢复之前的选择状态
                        item.Checked = selectedCombinations.Contains(combination);

                        itemsToAdd.Add(item);
                        index++;
                    }
                }
            }

            UpdateCombinationsList(itemsToAdd);
        }

        /// <summary>
        /// 更新组合列表（在UI线程中调用）
        /// </summary>
        private void UpdateCombinationsList(List<ListViewItem> items)
        {
            // 临时禁用事件处理，避免在重建列表时触发选择变化事件
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            listViewCombinations.Items.Clear();

            // 分批添加项目，避免UI冻结
            if (items.Count > 1000)
            {
                listViewCombinations.BeginUpdate();
                try
                {
                    listViewCombinations.Items.AddRange(items.ToArray());
                }
                finally
                {
                    listViewCombinations.EndUpdate();
                }
            }
            else
            {
                listViewCombinations.Items.AddRange(items.ToArray());
            }

            lblCombinationList.Text = $"具体组合（共 {listViewCombinations.Items.Count:N0} 个）：";

            // 调整列宽
            ListViewCombinations_Resize(listViewCombinations, EventArgs.Empty);

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;

            // 更新按钮状态
            UpdateUI();
        }



        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatus(string message, int progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, int>(UpdateStatus), message, progress);
                return;
            }

            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Blue;
            progressBar.Value = Math.Min(progress, 100);
        }

        /// <summary>
        /// 生成所有N数组合并统计频次
        /// </summary>
        private Dictionary<string, int> GenerateNCombinations(CancellationToken cancellationToken)
        {
            var combinations = new Dictionary<string, int>();
            int combinationCount = (int)numCombinationCount.Value;

            foreach (string line in originalData)
            {
                cancellationToken.ThrowIfCancellationRequested();

                string[] parts = line.Split('-');
                List<int> numbers = parts.Select(int.Parse).ToList();

                // 生成所有N数组合
                var nCombinations = GetCombinations(numbers, combinationCount);

                foreach (var combination in nCombinations)
                {
                    string combinationStr = string.Join("-", combination);
                    if (combinations.ContainsKey(combinationStr))
                    {
                        combinations[combinationStr]++;
                    }
                    else
                    {
                        combinations[combinationStr] = 1;
                    }
                }
            }

            return combinations;
        }



        /// <summary>
        /// 生成磁控点组合
        /// </summary>
        private List<string> GenerateMagneticPoints(List<string> validNCombinations, CancellationToken cancellationToken)
        {
            var magneticPoints = new List<string>();
            int magneticPointCount = (int)numMagneticPoints.Value;
            int maxNumber = (int)numMaxNumber.Value;

            // 创建所有可能的数字集合
            var allNumbers = Enumerable.Range(1, maxNumber).ToList();

            foreach (string nCombination in validNCombinations)
            {
                cancellationToken.ThrowIfCancellationRequested();

                string[] parts = nCombination.Split('-');
                var selectedNumbers = parts.Select(int.Parse).ToList();

                // 从剩余数字中选择需要的数量
                var remainingNumbers = allNumbers.Where(n => !selectedNumbers.Contains(n)).ToList();
                int needCount = magneticPointCount - selectedNumbers.Count;

                // 生成所有可能的组合
                if (needCount > 0)
                {
                    var combinations = GetCombinations(remainingNumbers, needCount);

                    foreach (var combination in combinations)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var fullCombination = new List<int>(selectedNumbers);
                        fullCombination.AddRange(combination);
                        fullCombination.Sort();

                        string magneticPoint = string.Join("-", fullCombination);
                        magneticPoints.Add(magneticPoint);
                    }
                }
                else if (needCount == 0)
                {
                    // 如果选中的组合数量正好等于磁控点位数，直接使用
                    var fullCombination = new List<int>(selectedNumbers);
                    fullCombination.Sort();
                    string magneticPoint = string.Join("-", fullCombination);
                    magneticPoints.Add(magneticPoint);
                }
            }

            return magneticPoints;
        }

        /// <summary>
        /// 获取组合（C(n,r)）- 优化版本
        /// </summary>
        private IEnumerable<List<int>> GetCombinations(List<int> list, int length)
        {
            if (length == 0)
                yield return new List<int>();
            else if (length == 1)
            {
                foreach (var item in list)
                    yield return new List<int> { item };
            }
            else
            {
                for (int i = 0; i <= list.Count - length; i++)
                {
                    var head = list[i];
                    var tail = list.Skip(i + 1).ToList();

                    foreach (var combination in GetCombinations(tail, length - 1))
                    {
                        var result = new List<int> { head };
                        result.AddRange(combination);
                        yield return result;
                    }
                }
            }
        }

        /// <summary>
        /// 统计磁控点频次
        /// </summary>
        private Dictionary<string, int> CountMagneticPoints(List<string> magneticPoints, CancellationToken cancellationToken)
        {
            var counts = new Dictionary<string, int>();

            foreach (string point in magneticPoints)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (counts.ContainsKey(point))
                {
                    counts[point]++;
                }
                else
                {
                    counts[point] = 1;
                }
            }

            return counts;
        }









        /// <summary>
        /// 导出选中组合按钮点击事件
        /// </summary>
        private async void btnExportSelected_Click(object sender, EventArgs e)
        {
            if (selectedCombinations.Count == 0)
            {
                MessageBox.Show("请先选择要导出的组合", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 确认是否继续计算磁控点
            int combinationCount = (int)numCombinationCount.Value;
            var result = MessageBox.Show($"将基于您选中的 {selectedCombinations.Count} 个{combinationCount}数组合计算磁控点分布。\n\n这可能需要一些时间，是否继续？",
                "确认计算", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes) return;

            isProcessing = true;
            cancellationTokenSource = new CancellationTokenSource();
            UpdateUI();

            try
            {
                await ProcessSelectedCombinationsAsync(cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                lblStatus.Text = "计算已取消";
                lblStatus.ForeColor = Color.Orange;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "计算失败";
                lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                isProcessing = false;
                progressBar.Value = 0;
                UpdateUI();
            }
        }

        /// <summary>
        /// 处理选中组合的磁控点计算
        /// </summary>
        private async Task ProcessSelectedCombinationsAsync(CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                // 步骤1: 将选中的组合转换为有效组合列表
                int combinationCount = (int)numCombinationCount.Value;
                UpdateStatus($"正在准备选中的{combinationCount}数组合...", 10);
                var validNCombinations = selectedCombinations.ToList();

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 生成磁控点组合
                UpdateStatus("正在生成磁控点组合...", 30);
                var magneticPoints = GenerateMagneticPoints(validNCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 统计磁控点频次
                UpdateStatus("正在统计磁控点频次...", 70);
                var magneticPointCounts = CountMagneticPoints(magneticPoints, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤4: 筛选最终结果（基于选中的组合，并按频次范围筛选）
                UpdateStatus("正在重新统计频次并筛选结果...", 85);
                var filteredResults = FilterResultsBySelectedCombinations(magneticPointCounts, validNCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤5: 导出结果
                UpdateStatus("正在导出结果...", 95);
                ExportMagneticPointResults(filteredResults);

                UpdateStatus($"计算完成，共生成 {filteredResults.Count} 个磁控点结果", 100);

            }, cancellationToken);
        }

        /// <summary>
        /// 基于选中组合筛选磁控点结果（优化版本）
        /// </summary>
        private List<string> FilterResultsBySelectedCombinations(Dictionary<string, int> magneticPointCounts,
            List<string> selectedNCombinations, CancellationToken cancellationToken)
        {
            // 根据数据量选择不同的处理策略
            if (magneticPointCounts.Count > 500000) // 超大数据量时使用并行处理
            {
                return FilterResultsBySelectedCombinationsParallel(magneticPointCounts, selectedNCombinations, cancellationToken);
            }
            else if (magneticPointCounts.Count > 100000) // 大数据量时使用流式处理
            {
                return FilterResultsBySelectedCombinationsLargeData(magneticPointCounts, selectedNCombinations, cancellationToken);
            }
            var selectedCombinationsSet = new HashSet<string>(selectedNCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;
            // 使用字典直接统计，避免创建大量重复字符串
            var newMagneticPointCounts = new Dictionary<string, int>();

            // 一次遍历完成筛选和统计
            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查磁控点是否包含至少一个选中的N数组合
                if (ContainsSelectedNCombinationOptimized(kvp.Key, selectedCombinationsSet))
                {
                    // 直接累加频次，避免创建重复字符串
                    if (newMagneticPointCounts.TryGetValue(kvp.Key, out int existingCount))
                    {
                        newMagneticPointCounts[kvp.Key] = existingCount + kvp.Value;
                    }
                    else
                    {
                        newMagneticPointCounts[kvp.Key] = kvp.Value;
                    }
                }
            }

            // 筛选并返回结果
            var results = new List<string>();
            foreach (var kvp in newMagneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查频次是否在设定范围内
                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 大数据量时的流式处理版本，避免内存溢出
        /// </summary>
        private List<string> FilterResultsBySelectedCombinationsLargeData(Dictionary<string, int> magneticPointCounts,
            List<string> selectedNCombinations, CancellationToken cancellationToken)
        {
            var selectedCombinationsSet = new HashSet<string>(selectedNCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用更大的初始容量减少重新分配
            var newMagneticPointCounts = new Dictionary<string, int>(magneticPointCounts.Count / 4);
            var results = new List<string>();

            // 分批处理，每批处理10000个项目
            const int batchSize = 10000;
            int processedCount = 0;

            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查磁控点是否包含至少一个选中的N数组合
                if (ContainsSelectedNCombinationOptimized(kvp.Key, selectedCombinationsSet))
                {
                    // 直接累加频次
                    if (newMagneticPointCounts.TryGetValue(kvp.Key, out int existingCount))
                    {
                        newMagneticPointCounts[kvp.Key] = existingCount + kvp.Value;
                    }
                    else
                    {
                        newMagneticPointCounts[kvp.Key] = kvp.Value;
                    }
                }

                processedCount++;

                // 每处理一批数据，检查是否需要进行中间筛选以释放内存
                if (processedCount % batchSize == 0)
                {
                    // 将符合条件的结果移到结果列表，并从临时字典中移除
                    var toRemove = new List<string>();
                    foreach (var tempKvp in newMagneticPointCounts)
                    {
                        if (tempKvp.Value >= minCount && tempKvp.Value <= maxCount)
                        {
                            results.Add(tempKvp.Key);
                            toRemove.Add(tempKvp.Key);
                        }
                    }

                    // 移除已处理的项目以释放内存
                    foreach (var key in toRemove)
                    {
                        newMagneticPointCounts.Remove(key);
                    }

                    // 强制垃圾回收（仅在大数据量时）
                    if (processedCount % (batchSize * 10) == 0)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                    }
                }
            }

            // 处理剩余的数据
            foreach (var kvp in newMagneticPointCounts)
            {
                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 超大数据量时的并行处理版本
        /// </summary>
        private List<string> FilterResultsBySelectedCombinationsParallel(Dictionary<string, int> magneticPointCounts,
            List<string> selectedNCombinations, CancellationToken cancellationToken)
        {
            var selectedCombinationsSet = new HashSet<string>(selectedNCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用并发字典支持多线程访问
            var newMagneticPointCounts = new ConcurrentDictionary<string, int>();

            // 并行处理磁控点筛选和统计
            var parallelOptions = new ParallelOptions
            {
                CancellationToken = cancellationToken,
                MaxDegreeOfParallelism = Environment.ProcessorCount // 限制并行度避免过度占用资源
            };

            try
            {
                Parallel.ForEach(magneticPointCounts, parallelOptions, kvp =>
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // 检查磁控点是否包含至少一个选中的N数组合
                    if (ContainsSelectedNCombinationOptimized(kvp.Key, selectedCombinationsSet))
                    {
                        // 使用AddOrUpdate进行线程安全的累加
                        newMagneticPointCounts.AddOrUpdate(kvp.Key, kvp.Value, (key, existingValue) => existingValue + kvp.Value);
                    }
                });
            }
            catch (OperationCanceledException)
            {
                throw; // 重新抛出取消异常
            }

            // 筛选结果
            var results = new List<string>();
            foreach (var kvp in newMagneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 检查磁控点是否包含选中的N数组合（.NET Framework兼容优化版本）
        /// </summary>
        private bool ContainsSelectedNCombinationOptimized(string magneticPoint, HashSet<string> selectedCombinations)
        {
            // 使用数组替代Span，兼容.NET Framework
            var numbers = new int[16]; // 假设最多16个数字，足够处理大部分情况
            int numberCount = 0;

            // 手动解析数字，避免Split和LINQ的开销
            int currentNumber = 0;
            bool hasNumber = false;

            for (int i = 0; i <= magneticPoint.Length; i++)
            {
                if (i == magneticPoint.Length || magneticPoint[i] == '-')
                {
                    if (hasNumber && numberCount < numbers.Length)
                    {
                        numbers[numberCount++] = currentNumber;
                    }
                    currentNumber = 0;
                    hasNumber = false;
                }
                else if (char.IsDigit(magneticPoint[i]))
                {
                    currentNumber = currentNumber * 10 + (magneticPoint[i] - '0');
                    hasNumber = true;
                }
            }

            // 检查所有N数组合，使用StringBuilder减少字符串分配
            int combinationCount = (int)numCombinationCount.Value;
            var sb = new StringBuilder(50); // 预分配更大容量

            // 生成所有N数组合并检查
            var numberList = new List<int>();
            for (int i = 0; i < numberCount; i++)
            {
                numberList.Add(numbers[i]);
            }

            var combinations = GetCombinations(numberList, combinationCount);
            foreach (var combination in combinations)
            {
                sb.Clear();
                for (int i = 0; i < combination.Count; i++)
                {
                    if (i > 0) sb.Append('-');
                    sb.Append(combination[i]);
                }

                if (selectedCombinations.Contains(sb.ToString()))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 高性能版本：使用缓存的字符串避免重复构造（适用于频繁调用的场景）
        /// </summary>
        private bool ContainsSelectedNCombinationCached(string magneticPoint, HashSet<string> selectedCombinations)
        {
            // 对于小数据量，直接使用Split可能更快（JIT优化）
            if (magneticPoint.Length < 20)
            {
                return ContainsSelectedNCombination(magneticPoint, selectedCombinations);
            }

            // 对于大数据量，使用优化版本
            return ContainsSelectedNCombinationOptimized(magneticPoint, selectedCombinations);
        }

        /// <summary>
        /// 检查磁控点是否包含选中的N数组合（保留原版本作为备用）
        /// </summary>
        private bool ContainsSelectedNCombination(string magneticPoint, HashSet<string> selectedCombinations)
        {
            string[] parts = magneticPoint.Split('-');
            List<int> numbers = parts.Select(int.Parse).ToList();
            int combinationCount = (int)numCombinationCount.Value;

            // 检查所有N数组合
            var combinations = GetCombinations(numbers, combinationCount);
            foreach (var combination in combinations)
            {
                string combinationStr = string.Join("-", combination);
                if (selectedCombinations.Contains(combinationStr))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 导出磁控点结果
        /// </summary>
        private void ExportMagneticPointResults(List<string> results)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<List<string>>(ExportMagneticPointResults), results);
                return;
            }

            if (results.Count == 0)
            {
                int combinationCount = (int)numCombinationCount.Value;
                MessageBox.Show($"基于选中的{combinationCount}数组合未生成任何磁控点结果", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            saveFileDialog.FileName = $"基于选中组合的磁控点结果_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var exportContent = new StringBuilder();
                    /*
                    exportContent.AppendLine("# 基于选中2数组合的磁控点分布运算结果");
                    exportContent.AppendLine($"# 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    exportContent.AppendLine($"# 参数设置:");
                    exportContent.AppendLine($"#   最大数值: {numMaxNumber.Value}");
                    exportContent.AppendLine($"#   磁控点位数: {numMagneticPoints.Value}");
                    exportContent.AppendLine($"#   基于选中的2数组合数量: {selectedCombinations.Count}");
                    exportContent.AppendLine($"#   最终结果频次范围: {numResultMin.Value}-{numResultMax.Value}");
                    exportContent.AppendLine($"# 磁控点结果数量: {results.Count}");
                    exportContent.AppendLine("#");
                    exportContent.AppendLine("# 处理逻辑:");
                    exportContent.AppendLine("#   1. 基于选中的2数组合生成所有磁控点组合");
                    exportContent.AppendLine("#   2. 重新统计每个6数字符串的出现次数");
                    exportContent.AppendLine("#   3. 筛选出现次数在设定范围内的结果");
                    exportContent.AppendLine("#");
                    exportContent.AppendLine("# 选中的2数组合:");

                    foreach (string combination in selectedCombinations.OrderBy(c => c))
                    {
                        exportContent.AppendLine($"#   {combination}");
                    }
                    exportContent.AppendLine("#");
                    exportContent.AppendLine("# 磁控点结果:");
                    exportContent.AppendLine("#");
                    */
                    // 按数字顺序排序导出
                    var sortedResults = results.OrderBy(r =>
                    {
                        var parts = r.Split('-');
                        return parts.Select(int.Parse).ToArray();
                    }, new ArrayComparer()).ToList();

                    foreach (string result in sortedResults)
                    {
                        exportContent.AppendLine(result);
                    }

                    File.WriteAllText(saveFileDialog.FileName, exportContent.ToString(), Encoding.UTF8);

                    MessageBox.Show($"磁控点结果已成功导出到:\n{saveFileDialog.FileName}\n\n共导出 {results.Count} 个磁控点组合",
                        "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = $"已导出 {results.Count} 个磁控点结果到: {Path.GetFileName(saveFileDialog.FileName)}";
                    lblStatus.ForeColor = Color.Green;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    lblStatus.Text = "导出失败";
                    lblStatus.ForeColor = Color.Red;
                }
            }
        }

        /// <summary>
        /// 获取选中的组合
        /// </summary>
        private List<dynamic> GetSelectedCombinations()
        {
            var result = new List<dynamic>();

            // 从全局选择集合中获取组合信息
            foreach (string combination in selectedCombinations)
            {
                if (nCombinationCounts.ContainsKey(combination))
                {
                    result.Add(new { Combination = combination, Frequency = nCombinationCounts[combination] });
                }
            }

            return result;
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            // 临时禁用事件处理
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            foreach (ListViewItem item in listViewCombinations.Items)
            {
                item.Checked = true;
                var itemData = (dynamic)item.Tag;
                selectedCombinations.Add(itemData.Combination);
            }

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;

            UpdateExportButtonState();
        }

        /// <summary>
        /// 组合位数改变事件
        /// </summary>
        private void NumCombinationCount_ValueChanged(object sender, EventArgs e)
        {
            UpdateUI();
            CheckExcludeModeComplexity();
        }

        /// <summary>
        /// 检查剔除模式的复杂度并显示警告
        /// </summary>
        private void CheckExcludeModeComplexity()
        {
            if (radioBtnExcludeMode.Checked)
            {
                int maxNumber = (int)numMaxNumber.Value;
                int combinationCount = (int)numCombinationCount.Value;

                // 计算总组合数 C(n,r)
                long totalCombinations = CalculateCombinations(maxNumber, combinationCount);

                if (totalCombinations > 100000)
                {
                    string warning = $"警告：{combinationCount}数组合在最大数值{maxNumber}下将产生{totalCombinations:N0}种组合。\n" +
                                   "剔除模式在大数据量下可能会很慢，建议使用范围模式。";

                    MessageBox.Show(warning, "性能警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        /// <summary>
        /// 计算组合数 C(n,r)
        /// </summary>
        private long CalculateCombinations(int n, int r)
        {
            if (r > n || r < 0) return 0;
            if (r == 0 || r == n) return 1;

            // 使用较小的r来减少计算量
            if (r > n - r) r = n - r;

            long result = 1;
            for (int i = 0; i < r; i++)
            {
                result = result * (n - i) / (i + 1);
            }
            return result;
        }

        /// <summary>
        /// 组合列表大小改变事件
        /// </summary>
        private void ListViewCombinations_Resize(object sender, EventArgs e)
        {
            if (listViewCombinations.Columns.Count >= 3)
            {
                // 计算可用宽度（减去滚动条和边框）
                int availableWidth = listViewCombinations.ClientSize.Width - 25;

                // 固定序号列和频次列的宽度
                int serialWidth = 80;
                int frequencyWidth = 100;

                // 组合列占用剩余空间
                int combinationWidth = Math.Max(200, availableWidth - serialWidth - frequencyWidth);

                listViewCombinations.Columns[0].Width = serialWidth;
                listViewCombinations.Columns[1].Width = combinationWidth;
                listViewCombinations.Columns[2].Width = frequencyWidth;
            }
        }

        /// <summary>
        /// 剔除模式选择改变事件
        /// </summary>
        private void RadioBtnExcludeMode_CheckedChanged(object sender, EventArgs e)
        {
            if (radioBtnExcludeMode.Checked)
            {
                CheckExcludeModeComplexity();
            }
        }

        /// <summary>
        /// 清空选择按钮点击事件
        /// </summary>
        private void btnClearSelection_Click(object sender, EventArgs e)
        {
            // 临时禁用事件处理
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            foreach (ListViewItem item in listViewCombinations.Items)
            {
                item.Checked = false;
                var itemData = (dynamic)item.Tag;
                selectedCombinations.Remove(itemData.Combination);
            }

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;

            UpdateExportButtonState();
        }
    }

    /// <summary>
    /// 数组比较器，用于正确排序磁控点组合
    /// </summary>
    public class ArrayComparer : IComparer<int[]>
    {
        public int Compare(int[] x, int[] y)
        {
            if (x == null && y == null) return 0;
            if (x == null) return -1;
            if (y == null) return 1;

            int minLength = Math.Min(x.Length, y.Length);
            for (int i = 0; i < minLength; i++)
            {
                int comparison = x[i].CompareTo(y[i]);
                if (comparison != 0) return comparison;
            }

            return x.Length.CompareTo(y.Length);
        }
    }
}
